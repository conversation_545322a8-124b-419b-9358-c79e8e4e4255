/**
 * @file pages/cluster/role/index.js
 * <AUTHOR> Console
 * @description 角色管理主页面
 */

import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {Tabs} from '@baidu/sui';
import ClusterRoleList from './list/cluster-role-list';
import RoleList from './list/role-list';
import './detail/index';

const {asComponent, invokeAppComp, invokeBceSanUI} = decorators;

const template = html`
    <template>
        <div class="cce-wrapper cce-list role-manage">
            <h2 class="title">角色</h2>
            <s-tabs active="{=active=}">
                <s-tabpane label="ClusterRole" key="clusterrole">
                    <cluster-role-list cluster-uuid="{{clusterUuid}}" />
                </s-tabpane>
                <s-tabpane label="Role" key="role">
                    <role-list cluster-uuid="{{clusterUuid}}" />
                </s-tabpane>
            </s-tabs>
        </div>
    </template>
`;

@asComponent('@cce-role-manage')
@invokeAppComp
@invokeBceSanUI
class RoleManage extends Component {
    static template = template;
    static components = {
        'cluster-role-list': ClusterRoleList,
        'role-list': RoleList,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
    };

    initData() {
        return {
            active: 'clusterrole',
            clusterUuid: '',
        };
    }

    attached() {
        // 从 URL 参数获取集群信息和激活的 tab
        const urlParams = new URLSearchParams(window.location.hash.split('?')[1]);
        const clusterUuid = urlParams.get('clusterUuid');
        const active = urlParams.get('active');

        this.data.set('clusterUuid', clusterUuid);

        // 如果 URL 中指定了 active 参数，使用它
        if (active && (active === 'role' || active === 'clusterrole')) {
            this.data.set('active', active);
        } else {
            // 如果没有 active 参数，检查是否从 Role 详情页面回退
            this.checkReferrerAndSetActiveTab();
        }
    }

    // 检查来源页面并设置激活的 tab
    checkReferrerAndSetActiveTab() {
        const referrer = document.referrer;

        // 如果来源页面是 Role 详情页面，激活 Role tab
        if (
            referrer &&
            referrer.includes('/cce/cluster/role/detail') &&
            referrer.includes('type=role')
        ) {
            this.data.set('active', 'role');
        }
        // 如果来源页面是 ClusterRole 详情页面，激活 ClusterRole tab
        else if (
            referrer &&
            referrer.includes('/cce/cluster/role/detail') &&
            referrer.includes('type=clusterrole')
        ) {
            this.data.set('active', 'clusterrole');
        }
    }

    // Tab 切换事件处理（如果需要的话）
    onTabChange(e) {
        this.data.set('active', e.value);
    }
}

export default RoleManage;
